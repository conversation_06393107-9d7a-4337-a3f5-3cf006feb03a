<template>
  <div class="flex flex-col h-screen">
    <TopNav />
    <div class="flex flex-1 overflow-hidden">
      <SideNav />
      <router-view class="flex-1 p-4 overflow-auto" />
    </div>
  </div>
</template>

<script setup>
import TopNav from './components/TopNav.vue'
import SideNav from './components/SideNav.vue'
</script>

<style scoped>
/* 可设置全局布局相关样式，比如去掉默认边距等 */
body {
  margin: 0;
}
</style>
<template>
    <div>
        <h1 class="text-2xl mb-4">支撑应用管理</h1>
        <button class="bg-blue-500 text-white px-4 py-2" @click="showModal = !showModal">添加设备</button>
        <Teleport to="body">
            <AddDeviceModal v-if="showModal" @close="showModal = false" />
        </Teleport>
        <!-- 其他静态内容 -->
    </div>
</template>

<script setup>
import AddDeviceModal from '../views/AddDeviceModal.vue'
import { ref } from 'vue'
const showModal = ref(false)
</script>

<style scoped>
/* 样式 */
</style>
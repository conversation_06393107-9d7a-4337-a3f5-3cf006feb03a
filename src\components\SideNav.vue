<template>
    <aside class="w-64 bg-gray-100 p-4">
        <ul class="space-y-2">
            <li><router-link to="/public-place-monitor">公共场所在线监测</router-link></li>
            <li><router-link to="/radiation-health-monitor">放射卫生在线监测</router-link></li>
            <!-- 其他侧边菜单项，根据设计图对应页面路由写 -->
        </ul>
    </aside>
</template>

<script setup>
import { useRouter } from 'vue-router'
const router = useRouter()
</script>

<style scoped>
/* 自定义样式补充 */
</style>
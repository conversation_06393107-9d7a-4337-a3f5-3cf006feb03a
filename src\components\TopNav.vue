<template>
    <nav class="flex justify-center items-center bg-blue-500 text-white py-2">
        <ul class="flex space-x-4">
            <li><router-link to="/public-place-monitor">公共场所在线监测</router-link></li>
            <li><router-link to="/radiation-health-monitor">放射卫生在线监测</router-link></li>
            <li><router-link to="/">漳平市卫生监督管理系统</router-link></li>
            <li><router-link to="/occupational-hazard-monitor">职业病危害因素在线监测</router-link></li>
            <li><router-link to="/real-time-video-monitor">实时视频监控</router-link></li>
            <li><router-link to="/support-app-management">支撑应用管理</router-link></li>
        </ul>
    </nav>
</template>

<script setup>
// 可在这里写一些交互逻辑，比如激活态样式控制等（结合路由元信息或当前路由判断）
import { useRouter } from 'vue-router'
const router = useRouter()
// 若需要动态判断激活项，可监听 router.currentRoute
</script>

<style scoped>
/* 若需要自定义样式补充 Tailwind  */
</style>